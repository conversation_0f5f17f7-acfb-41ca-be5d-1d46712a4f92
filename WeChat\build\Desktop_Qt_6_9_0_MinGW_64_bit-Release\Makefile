#############################################################################
# Makefile for building: WeChat
# Generated by qmake (3.1) (Qt 6.9.0)
# Project:  ..\..\WeChat.pro
# Template: app
# Command: D:\PERSONAL\QT\6.9.0\mingw_64\bin\qmake.exe -o Makefile ..\..\WeChat.pro -spec win32-g++ "CONFIG+=qtquickcompiler"
#############################################################################

MAKEFILE      = Makefile

EQ            = =

first: release
install: release-install
uninstall: release-uninstall
QMAKE         = D:\PERSONAL\QT\6.9.0\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\PERSONAL\QT\6.9.0\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\PERSONAL\QT\6.9.0\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move
SUBTARGETS    =  \
		release \
		debug


release: FORCE
	$(MAKE) -f $(MAKEFILE).Release
release-make_first: FORCE
	$(MAKE) -f $(MAKEFILE).Release 
release-all: FORCE
	$(MAKE) -f $(MAKEFILE).Release all
release-clean: FORCE
	$(MAKE) -f $(MAKEFILE).Release clean
release-distclean: FORCE
	$(MAKE) -f $(MAKEFILE).Release distclean
release-install: FORCE
	$(MAKE) -f $(MAKEFILE).Release install
release-uninstall: FORCE
	$(MAKE) -f $(MAKEFILE).Release uninstall
debug: FORCE
	$(MAKE) -f $(MAKEFILE).Debug
debug-make_first: FORCE
	$(MAKE) -f $(MAKEFILE).Debug 
debug-all: FORCE
	$(MAKE) -f $(MAKEFILE).Debug all
debug-clean: FORCE
	$(MAKE) -f $(MAKEFILE).Debug clean
debug-distclean: FORCE
	$(MAKE) -f $(MAKEFILE).Debug distclean
debug-install: FORCE
	$(MAKE) -f $(MAKEFILE).Debug install
debug-uninstall: FORCE
	$(MAKE) -f $(MAKEFILE).Debug uninstall

Makefile: ../../WeChat.pro D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++/qmake.conf D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/spec_pre.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/device_config.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/sanitize.conf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/gcc-base.conf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/g++-base.conf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/win32/windows_vulkan_sdk.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/windows-vulkan.conf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/g++-win32.conf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/windows-desktop.conf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/qconfig.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_ext_freetype.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_ext_libjpeg.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_ext_libpng.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_ext_openxr_loader.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_charts.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_charts_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_chartsqml.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_chartsqml_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_concurrent.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_core.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_core_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_dbus.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_dbus_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_designer.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_designer_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_entrypoint_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_example_icons_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_examples_asset_downloader_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_ffmpegmediapluginimpl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_freetype_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_gui.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_gui_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_harfbuzz_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_help.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_help_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_jpeg_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsanimation.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsanimation_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsfolderlistmodel.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsfolderlistmodel_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsplatform.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsplatform_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsqmlmodels.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsqmlmodels_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labssettings.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labssettings_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labssharedimage.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labssharedimage_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labswavefrontmesh.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labswavefrontmesh_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_linguist.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimedia.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimediaquick_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimediatestlibprivate_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_network.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_network_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_opengl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_opengl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_openglwidgets.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_openglwidgets_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_png_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_printsupport.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qdoccatch_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qdoccatchconversions_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qdoccatchgenerators_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qml.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qml_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlassetdownloader.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlassetdownloader_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlcompiler.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlcompiler_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlcore.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlcore_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmldom_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlformat_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlintegration.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlintegration_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmllocalstorage.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmllocalstorage_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlls_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlmeta.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlmeta_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlmodels.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlnetwork.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlnetwork_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmltest.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmltoolingsettings_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmltyperegistrar_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlxmllistmodel.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlxmllistmodel_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3d.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3d_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dassetimport.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dassetimport_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dassetutils.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dassetutils_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3deffects.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3deffects_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dglslparser_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dhelpers.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dhelpers_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dhelpersimpl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dhelpersimpl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3diblbaker.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3diblbaker_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dparticleeffects.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dparticleeffects_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dparticles.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dparticles_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3druntimerender.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3druntimerender_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dspatialaudio_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dutils.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dutils_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dxr.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dxr_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basic.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basic_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusion.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusion_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imagine.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imagine_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2impl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2impl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2material.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2material_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universal.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universal_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2windowsstyleimpl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2windowsstyleimpl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrolstestutilsprivate_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2quickimpl.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2quickimpl_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2utils.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2utils_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickeffects.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickeffects_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicklayouts.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicklayouts_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktestutilsprivate_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktimeline.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktimeline_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktimelineblendtrees.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktimelineblendtrees_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickvectorimage.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickvectorimage_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickvectorimagegenerator_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_shadertools.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_shadertools_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_spatialaudio.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_spatialaudio_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_sql.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_sql_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_svg.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_svg_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_svgwidgets.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_svgwidgets_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_testinternals_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_testlib.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_testlib_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_tools_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_uiplugin.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_uitools.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_uitools_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_widgets.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_widgets_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_xml.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_xml_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_zlib_private.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/qt_functions.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/qt_config.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++/qmake.conf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/spec_post.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/exclusive_builds.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/toolchain.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/default_pre.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/win32/default_pre.prf \
		../../netapi/netapi.pri \
		../../MD5/md5.pri \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/resolve_config.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/exclusive_builds_post.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/default_post.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/qtquickcompiler.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/precompile_header.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/warn_on.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/permissions.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/qt.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/resources_functions.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/resources.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/moc.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/win32/opengl.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/uic.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/qmake_use.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/file_copies.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/win32/windows.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/testcase_targets.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/exceptions.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/yacc.prf \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/lex.prf \
		../../WeChat.pro \
		D:/PERSONAL/QT/6.9.0/mingw_64/lib/Qt6Widgets.prl \
		D:/PERSONAL/QT/6.9.0/mingw_64/lib/Qt6Gui.prl \
		D:/PERSONAL/QT/6.9.0/mingw_64/lib/Qt6Network.prl \
		D:/PERSONAL/QT/6.9.0/mingw_64/lib/Qt6Core.prl \
		D:/PERSONAL/QT/6.9.0/mingw_64/lib/Qt6EntryPoint.prl \
		.qmake.stash \
		D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/build_pass.prf \
		../../resource.qrc
	$(QMAKE) -o Makefile ..\..\WeChat.pro -spec win32-g++ "CONFIG+=qtquickcompiler"
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/spec_pre.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/device_config.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/sanitize.conf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/gcc-base.conf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/g++-base.conf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/win32/windows_vulkan_sdk.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/windows-vulkan.conf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/g++-win32.conf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/common/windows-desktop.conf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/qconfig.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_ext_freetype.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_ext_libjpeg.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_ext_libpng.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_ext_openxr_loader.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_charts.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_charts_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_chartsqml.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_chartsqml_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_concurrent.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_concurrent_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_core.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_core_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_dbus.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_dbus_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_designer.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_designer_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_designercomponents_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_entrypoint_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_example_icons_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_examples_asset_downloader_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_fb_support_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_ffmpegmediapluginimpl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_freetype_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_gui.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_gui_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_harfbuzz_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_help.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_help_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_jpeg_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsanimation.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsanimation_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsfolderlistmodel.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsfolderlistmodel_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsplatform.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsplatform_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsqmlmodels.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labsqmlmodels_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labssettings.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labssettings_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labssharedimage.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labssharedimage_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labswavefrontmesh.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_labswavefrontmesh_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_linguist.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimedia.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimedia_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimediaquick_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimediatestlibprivate_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimediawidgets.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_network.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_network_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_opengl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_opengl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_openglwidgets.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_openglwidgets_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_packetprotocol_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_png_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_printsupport.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_printsupport_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qdoccatch_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qdoccatchconversions_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qdoccatchgenerators_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qml.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qml_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlassetdownloader.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlassetdownloader_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlcompiler.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlcompiler_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlcore.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlcore_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmldebug_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmldom_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlformat_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlintegration.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlintegration_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmllocalstorage.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmllocalstorage_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlls_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlmeta.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlmeta_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlmodels.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlmodels_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlnetwork.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlnetwork_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmltest.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmltest_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmltoolingsettings_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmltyperegistrar_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlworkerscript.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlxmllistmodel.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_qmlxmllistmodel_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3d.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3d_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dassetimport.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dassetimport_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dassetutils.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dassetutils_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3deffects.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3deffects_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dglslparser_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dhelpers.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dhelpers_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dhelpersimpl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dhelpersimpl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3diblbaker.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3diblbaker_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dparticleeffects.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dparticleeffects_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dparticles.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dparticles_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3druntimerender.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3druntimerender_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dspatialaudio_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dutils.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dutils_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dxr.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick3dxr_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quick_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basic.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basic_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusion.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusion_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imagine.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imagine_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2impl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2impl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2material.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2material_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universal.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universal_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2windowsstyleimpl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrols2windowsstyleimpl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickcontrolstestutilsprivate_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2quickimpl.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2quickimpl_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2utils.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickdialogs2utils_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickeffects.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickeffects_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicklayouts.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicklayouts_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickparticles_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickshapes_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktemplates2.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktestutilsprivate_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktimeline.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktimeline_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktimelineblendtrees.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quicktimelineblendtrees_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickvectorimage.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickvectorimage_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickvectorimagegenerator_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickwidgets.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_quickwidgets_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_shadertools.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_shadertools_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_spatialaudio.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_spatialaudio_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_sql.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_sql_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_svg.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_svg_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_svgwidgets.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_svgwidgets_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_testinternals_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_testlib.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_testlib_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_tools_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_uiplugin.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_uitools.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_uitools_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_widgets.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_widgets_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_xml.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_xml_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/modules/qt_lib_zlib_private.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/qt_functions.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/qt_config.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++/qmake.conf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/spec_post.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/exclusive_builds.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/toolchain.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/default_pre.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/win32/default_pre.prf:
../../netapi/netapi.pri:
../../MD5/md5.pri:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/resolve_config.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/exclusive_builds_post.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/default_post.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/qtquickcompiler.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/precompile_header.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/warn_on.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/permissions.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/qt.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/resources_functions.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/resources.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/moc.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/win32/opengl.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/uic.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/qmake_use.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/file_copies.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/win32/windows.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/testcase_targets.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/exceptions.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/yacc.prf:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/lex.prf:
../../WeChat.pro:
D:/PERSONAL/QT/6.9.0/mingw_64/lib/Qt6Widgets.prl:
D:/PERSONAL/QT/6.9.0/mingw_64/lib/Qt6Gui.prl:
D:/PERSONAL/QT/6.9.0/mingw_64/lib/Qt6Network.prl:
D:/PERSONAL/QT/6.9.0/mingw_64/lib/Qt6Core.prl:
D:/PERSONAL/QT/6.9.0/mingw_64/lib/Qt6EntryPoint.prl:
.qmake.stash:
D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/build_pass.prf:
../../resource.qrc:
qmake: FORCE
	@$(QMAKE) -o Makefile ..\..\WeChat.pro -spec win32-g++ "CONFIG+=qtquickcompiler"

qmake_all: FORCE

make_first: release-make_first debug-make_first  FORCE
all: release-all debug-all  FORCE
clean: release-clean debug-clean  FORCE
distclean: release-distclean debug-distclean  FORCE
	-$(DEL_FILE) Makefile
	-$(DEL_FILE) .qmake.stash

release-mocclean:
	$(MAKE) -f $(MAKEFILE).Release mocclean
debug-mocclean:
	$(MAKE) -f $(MAKEFILE).Debug mocclean
mocclean: release-mocclean debug-mocclean

release-mocables:
	$(MAKE) -f $(MAKEFILE).Release mocables
debug-mocables:
	$(MAKE) -f $(MAKEFILE).Debug mocables
mocables: release-mocables debug-mocables

check: first

benchmark: first
FORCE:

.SUFFIXES:

$(MAKEFILE).Release: Makefile
$(MAKEFILE).Debug: Makefile

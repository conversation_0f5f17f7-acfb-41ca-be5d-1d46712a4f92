#include "wechatdialog.h"
#include "ui_wechatdialog.h"
#include <QMessageBox> //消息窗口
#include "qDebug"
WeChatDialog::WeChatDialog(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::WeChatDialog)
{
    ui->setupUi(this);
    setWindowTitle("视频会议v1.0");
}

WeChatDialog::~WeChatDialog()
{
    qDebug()<<__func__;
    delete ui;
}
//关闭事件
void WeChatDialog::closeEvent(QCloseEvent *event)
{
    qDebug()<<__func__;
    if(QMessageBox::question(this,"提示","是否退出？") == QMessageBox::Yes)
    {
        Q_EMIT SIG_CLOSE();  //发送信号
        event->accept();     //执行关闭事件
    }else{
        event->ignore();
    }
    // event->accept();  //执行
    // event->ignore();  //忽略

}

void WeChatDialog::setInfo(QString name, int icon)
{
    //设置名字和头像
    ui->lb_name->setText(name);
}
//创建会议
void WeChatDialog::on_tb_create_clicked()
{
    Q_EMIT SIG_createRoom();
}

//加入会议
void WeChatDialog::on_tb_join_clicked()
{
    Q_EMIT SIG_joinRoom();
}


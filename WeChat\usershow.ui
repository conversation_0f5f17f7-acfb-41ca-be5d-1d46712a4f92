<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UserShow</class>
 <widget class="QWidget" name="UserShow">
  <property name="enabled">
   <bool>false</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>230</width>
    <height>200</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>200</width>
    <height>200</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1000000</width>
    <height>1000000</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(0, 0, 0);
color: rgb(255, 255, 255);</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="lb_name">
     <property name="enabled">
      <bool>false</bool>
     </property>
     <property name="minimumSize">
      <size>
       <width>200</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>30</height>
      </size>
     </property>
     <property name="text">
      <string>用户名：测试用户</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>160</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

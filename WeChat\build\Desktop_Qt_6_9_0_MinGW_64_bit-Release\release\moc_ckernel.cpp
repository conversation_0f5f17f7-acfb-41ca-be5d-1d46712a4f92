/****************************************************************************
** Meta object code from reading C++ file 'ckernel.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../ckernel.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ckernel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN7CkernelE_t {};
} // unnamed namespace

template <> constexpr inline auto Ckernel::qt_create_metaobjectdata<qt_meta_tag_ZN7CkernelE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "Ckernel",
        "setNetPackMap",
        "",
        "initConfig",
        "slot_destroy",
        "slot_loginCommit",
        "tel",
        "pass",
        "slot_registerCommit",
        "name",
        "slot_dealData",
        "sock",
        "char*",
        "buf",
        "nlen",
        "slot_dealLoginRs",
        "slot_dealRegisterRs",
        "slot_createRoom",
        "slot_joinRoom",
        "slot_quitRoom",
        "slot_dealCreateRoomRs",
        "slot_dealJoinCRoomRs",
        "slot_dealRoomMemberRq",
        "slot_dealLeaveRoomRq"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'setNetPackMap'
        QtMocHelpers::SlotData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'initConfig'
        QtMocHelpers::SlotData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'slot_destroy'
        QtMocHelpers::SlotData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'slot_loginCommit'
        QtMocHelpers::SlotData<void(QString, QString)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::QString, 7 },
        }}),
        // Slot 'slot_registerCommit'
        QtMocHelpers::SlotData<void(QString, QString, QString)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 9 }, { QMetaType::QString, 6 }, { QMetaType::QString, 7 },
        }}),
        // Slot 'slot_dealData'
        QtMocHelpers::SlotData<void(uint, char *, int)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 }, { 0x80000000 | 12, 13 }, { QMetaType::Int, 14 },
        }}),
        // Slot 'slot_dealLoginRs'
        QtMocHelpers::SlotData<void(uint, char *, int)>(15, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 }, { 0x80000000 | 12, 13 }, { QMetaType::Int, 14 },
        }}),
        // Slot 'slot_dealRegisterRs'
        QtMocHelpers::SlotData<void(uint, char *, int)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 }, { 0x80000000 | 12, 13 }, { QMetaType::Int, 14 },
        }}),
        // Slot 'slot_createRoom'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'slot_joinRoom'
        QtMocHelpers::SlotData<void()>(18, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'slot_quitRoom'
        QtMocHelpers::SlotData<void()>(19, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'slot_dealCreateRoomRs'
        QtMocHelpers::SlotData<void(uint, char *, int)>(20, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 }, { 0x80000000 | 12, 13 }, { QMetaType::Int, 14 },
        }}),
        // Slot 'slot_dealJoinCRoomRs'
        QtMocHelpers::SlotData<void(uint, char *, int)>(21, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 }, { 0x80000000 | 12, 13 }, { QMetaType::Int, 14 },
        }}),
        // Slot 'slot_dealRoomMemberRq'
        QtMocHelpers::SlotData<void(uint, char *, int)>(22, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 }, { 0x80000000 | 12, 13 }, { QMetaType::Int, 14 },
        }}),
        // Slot 'slot_dealLeaveRoomRq'
        QtMocHelpers::SlotData<void(uint, char *, int)>(23, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 11 }, { 0x80000000 | 12, 13 }, { QMetaType::Int, 14 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<Ckernel, qt_meta_tag_ZN7CkernelE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject Ckernel::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN7CkernelE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN7CkernelE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN7CkernelE_t>.metaTypes,
    nullptr
} };

void Ckernel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<Ckernel *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->setNetPackMap(); break;
        case 1: _t->initConfig(); break;
        case 2: _t->slot_destroy(); break;
        case 3: _t->slot_loginCommit((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 4: _t->slot_registerCommit((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 5: _t->slot_dealData((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<char*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 6: _t->slot_dealLoginRs((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<char*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 7: _t->slot_dealRegisterRs((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<char*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 8: _t->slot_createRoom(); break;
        case 9: _t->slot_joinRoom(); break;
        case 10: _t->slot_quitRoom(); break;
        case 11: _t->slot_dealCreateRoomRs((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<char*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 12: _t->slot_dealJoinCRoomRs((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<char*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 13: _t->slot_dealRoomMemberRq((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<char*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 14: _t->slot_dealLeaveRoomRq((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<char*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        default: ;
        }
    }
}

const QMetaObject *Ckernel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Ckernel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN7CkernelE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int Ckernel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 15;
    }
    return _id;
}
QT_WARNING_POP

/********************************************************************************
** Form generated from reading UI file 'usershow.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_USERSHOW_H
#define UI_USERSHOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QLabel>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_UserShow
{
public:
    QVBoxLayout *verticalLayout;
    QLabel *lb_name;
    QSpacerItem *verticalSpacer;

    void setupUi(QWidget *UserShow)
    {
        if (UserShow->objectName().isEmpty())
            UserShow->setObjectName("UserShow");
        UserShow->setEnabled(false);
        UserShow->resize(230, 200);
        UserShow->setMinimumSize(QSize(200, 200));
        UserShow->setMaximumSize(QSize(1000000, 1000000));
        UserShow->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 0, 0);\n"
"color: rgb(255, 255, 255);"));
        verticalLayout = new QVBoxLayout(UserShow);
        verticalLayout->setObjectName("verticalLayout");
        lb_name = new QLabel(UserShow);
        lb_name->setObjectName("lb_name");
        lb_name->setEnabled(false);
        lb_name->setMinimumSize(QSize(200, 30));
        lb_name->setMaximumSize(QSize(16777215, 30));
        lb_name->setAlignment(Qt::AlignmentFlag::AlignCenter);

        verticalLayout->addWidget(lb_name);

        verticalSpacer = new QSpacerItem(20, 160, QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

        verticalLayout->addItem(verticalSpacer);


        retranslateUi(UserShow);

        QMetaObject::connectSlotsByName(UserShow);
    } // setupUi

    void retranslateUi(QWidget *UserShow)
    {
        UserShow->setWindowTitle(QCoreApplication::translate("UserShow", "Form", nullptr));
        lb_name->setText(QCoreApplication::translate("UserShow", "\347\224\250\346\210\267\345\220\215\357\274\232\346\265\213\350\257\225\347\224\250\346\210\267", nullptr));
    } // retranslateUi

};

namespace Ui {
    class UserShow: public Ui_UserShow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_USERSHOW_H

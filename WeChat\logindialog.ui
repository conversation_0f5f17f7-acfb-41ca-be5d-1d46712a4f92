<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LoginDialog</class>
 <widget class="QDialog" name="LoginDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QTabWidget" name="tw_page">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>10</y>
     <width>400</width>
     <height>560</height>
    </rect>
   </property>
   <property name="maximumSize">
    <size>
     <width>400</width>
     <height>560</height>
    </size>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="currentIndex">
    <number>1</number>
   </property>
   <widget class="QWidget" name="page_1">
    <attribute name="title">
     <string>登录</string>
    </attribute>
    <widget class="QPushButton" name="pb_clear">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>320</y>
       <width>93</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>清空</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pb_commit">
     <property name="geometry">
      <rect>
       <x>270</x>
       <y>320</y>
       <width>93</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>提交</string>
     </property>
    </widget>
    <widget class="QLabel" name="lb_tel">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>120</y>
       <width>69</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>手机号：</string>
     </property>
    </widget>
    <widget class="QLabel" name="lb_password">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>200</y>
       <width>69</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>密    码：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="le_tel">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>120</y>
       <width>270</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>133456780</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="le_password">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>200</y>
       <width>270</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>123456</string>
     </property>
     <property name="echoMode">
      <enum>QLineEdit::EchoMode::Password</enum>
     </property>
     <property name="clearButtonEnabled">
      <bool>true</bool>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_2">
    <attribute name="title">
     <string>注册</string>
    </attribute>
    <widget class="QPushButton" name="pb_clear_register">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>370</y>
       <width>93</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>清空</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pb_commit_register">
     <property name="geometry">
      <rect>
       <x>270</x>
       <y>370</y>
       <width>93</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>提交</string>
     </property>
    </widget>
    <widget class="QLabel" name="lb_tel_register">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>150</y>
       <width>69</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>手机号：</string>
     </property>
    </widget>
    <widget class="QLabel" name="lb_password_register">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>210</y>
       <width>69</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>密   码：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="le_tel_register">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>150</y>
       <width>270</width>
       <height>30</height>
      </rect>
     </property>
     <property name="toolTip">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;输入8-11位手机号&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLineEdit" name="le_password_register">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>210</y>
       <width>270</width>
       <height>30</height>
      </rect>
     </property>
     <property name="toolTip">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;输入20位以内密码&lt;/p&gt;&lt;p&gt;可以是字符或数字&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="echoMode">
      <enum>QLineEdit::EchoMode::Password</enum>
     </property>
     <property name="clearButtonEnabled">
      <bool>true</bool>
     </property>
    </widget>
    <widget class="QLabel" name="lb_confirm_register">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>270</y>
       <width>69</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>确   认：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="le_confirm_register">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>270</y>
       <width>270</width>
       <height>30</height>
      </rect>
     </property>
     <property name="toolTip">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;确认密码，保证两次输入一致&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="echoMode">
      <enum>QLineEdit::EchoMode::Password</enum>
     </property>
     <property name="clearButtonEnabled">
      <bool>true</bool>
     </property>
    </widget>
    <widget class="QLabel" name="lb_name_register">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>90</y>
       <width>69</width>
       <height>30</height>
      </rect>
     </property>
     <property name="text">
      <string>昵     称：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="le_name_register">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>90</y>
       <width>270</width>
       <height>30</height>
      </rect>
     </property>
     <property name="toolTip">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;输入10位以内昵称&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
    </widget>
   </widget>
  </widget>
  <widget class="QLabel" name="lb_icon">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>10</y>
     <width>300</width>
     <height>560</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap resource="resource.qrc">:/images/register.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
 </widget>
 <tabstops>
  <tabstop>le_name_register</tabstop>
  <tabstop>le_tel_register</tabstop>
  <tabstop>le_password_register</tabstop>
  <tabstop>le_confirm_register</tabstop>
  <tabstop>pb_clear_register</tabstop>
  <tabstop>pb_commit_register</tabstop>
  <tabstop>tw_page</tabstop>
  <tabstop>le_tel</tabstop>
  <tabstop>le_password</tabstop>
  <tabstop>pb_clear</tabstop>
  <tabstop>pb_commit</tabstop>
 </tabstops>
 <resources>
  <include location="resource.qrc"/>
 </resources>
 <connections/>
</ui>

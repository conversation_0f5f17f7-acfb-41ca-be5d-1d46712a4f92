#############################################################################
# Makefile for building: WeChatServer
# Generated by qmake (3.1) (Qt 6.4.2)
# Project:  ../../WeChatServer.pro
# Template: app
# Command: /usr/bin/qmake6 -o Makefile ../../WeChatServer.pro -spec linux-g++ CONFIG+=debug CONFIG+=qml_debug
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DQT_QML_DEBUG
CFLAGS        = -pipe -g -Wall -Wextra $(DEFINES)
CXXFLAGS      = -pipe -g -std=gnu++1z -Wall -Wextra $(DEFINES)
INCPATH       = -I../../../WeChatServer -I. -I../../include -I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++
QMAKE         = /usr/bin/qmake6
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /usr/bin/qmake6 -install qinstall
QINSTALL_PROGRAM = /usr/bin/qmake6 -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = WeChatServer1.0.0
DISTDIR = /home/<USER>/Study/WeChat/WeChatServer/build/Ubuntu_Desktop-Debug/.tmp/WeChatServer1.0.0
LINK          = g++
LFLAGS        = 
LIBS          = $(SUBLIBS) -lpthread -lmysqlclient   
AR            = ar cqs
RANLIB        = 
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = ../../src/Mysql.cpp \
		../../src/TCPKernel.cpp \
		../../src/Thread_pool.cpp \
		../../src/block_epoll_net.cpp \
		../../src/clogic.cpp \
		../../src/err_str.cpp \
		../../src/main.cpp 
OBJECTS       = Mysql.o \
		TCPKernel.o \
		Thread_pool.o \
		block_epoll_net.o \
		clogic.o \
		err_str.o \
		main.o
DIST          = /usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/spec_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/unix.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/linux.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/sanitize.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/gcc-base.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/gcc-base-unix.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/g++-base.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/g++-unix.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/qconfig.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_core.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_core_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_dbus.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_designer.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_eglfs_kms_gbm_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_gui.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_help.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_input_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_linguist.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_network.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_network_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_opengl.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_openglwidgets.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_openglwidgets_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_sql.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_testlib.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_uiplugin.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_uitools.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_widgets.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_xml.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qt_functions.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qt_config.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++/qmake.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/spec_post.prf \
		../../.qmake.stash \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/exclusive_builds.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/toolchain.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/default_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/resolve_config.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/default_post.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qml_debug.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/warn_on.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qmake_use.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/file_copies.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/testcase_targets.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/exceptions.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/yacc.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/lex.prf \
		../../../../WeChatServer.pro include/Mysql.h \
		include/TCPKernel.h \
		include/Thread_pool.h \
		include/block_epoll_net.h \
		include/clogic.h \
		include/err_str.h \
		include/packdef.h ../../src/Mysql.cpp \
		../../src/TCPKernel.cpp \
		../../src/Thread_pool.cpp \
		../../src/block_epoll_net.cpp \
		../../src/clogic.cpp \
		../../src/err_str.cpp \
		../../src/main.cpp
QMAKE_TARGET  = WeChatServer
DESTDIR       = 
TARGET        = WeChatServer


first: all
####### Build rules

WeChatServer:  $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET)  $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ../../WeChatServer.pro /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++/qmake.conf /usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/spec_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/unix.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/linux.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/sanitize.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/gcc-base.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/gcc-base-unix.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/g++-base.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/g++-unix.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/qconfig.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_core.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_core_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_dbus.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_designer.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_eglfs_kms_gbm_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_gui.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_help.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_input_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_linguist.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_network.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_network_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_opengl.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_openglwidgets.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_openglwidgets_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_sql.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_testlib.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_uiplugin.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_uitools.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_widgets.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_xml.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qt_functions.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qt_config.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++/qmake.conf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/spec_post.prf \
		.qmake.stash \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/exclusive_builds.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/toolchain.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/default_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/resolve_config.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/default_post.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qml_debug.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/warn_on.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qmake_use.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/file_copies.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/testcase_targets.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/exceptions.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/yacc.prf \
		/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/lex.prf \
		../../WeChatServer.pro
	$(QMAKE) -o Makefile ../../WeChatServer.pro -spec linux-g++ CONFIG+=debug CONFIG+=qml_debug
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/spec_pre.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/unix.conf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/linux.conf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/sanitize.conf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/gcc-base.conf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/gcc-base-unix.conf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/g++-base.conf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/g++-unix.conf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/qconfig.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_concurrent.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_concurrent_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_core.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_core_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_dbus.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_dbus_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_designer.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_eglfs_kms_gbm_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_fb_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_gui.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_gui_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_help.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_input_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_kms_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_linguist.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_network.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_network_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_opengl.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_opengl_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_openglwidgets.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_openglwidgets_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_printsupport.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_printsupport_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_sql.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_sql_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_testlib.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_testlib_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_uiplugin.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_uitools.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_widgets.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_widgets_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_xml.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/modules/qt_lib_xml_private.pri:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qt_functions.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qt_config.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++/qmake.conf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/spec_post.prf:
.qmake.stash:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/exclusive_builds.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/toolchain.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/default_pre.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/resolve_config.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/default_post.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qml_debug.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/warn_on.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/qmake_use.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/file_copies.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/testcase_targets.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/exceptions.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/yacc.prf:
/usr/lib/x86_64-linux-gnu/qt6/mkspecs/features/lex.prf:
../../WeChatServer.pro:
qmake: FORCE
	@$(QMAKE) -o Makefile ../../WeChatServer.pro -spec linux-g++ CONFIG+=debug CONFIG+=qml_debug

qmake_all: FORCE


all: Makefile WeChatServer

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) Makefile


####### Sub-libraries

check: first

benchmark: first

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: 

####### Compile

Mysql.o: ../../src/Mysql.cpp ../../include/Mysql.h \
		../../include/packdef.h \
		../../include/err_str.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o Mysql.o ../../src/Mysql.cpp

TCPKernel.o: ../../src/TCPKernel.cpp ../../include/TCPKernel.h \
		../../include/block_epoll_net.h \
		../../include/Thread_pool.h \
		../../include/packdef.h \
		../../include/err_str.h \
		../../include/Mysql.h \
		../../include/clogic.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o TCPKernel.o ../../src/TCPKernel.cpp

Thread_pool.o: ../../src/Thread_pool.cpp ../../include/Thread_pool.h \
		../../include/packdef.h \
		../../include/err_str.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o Thread_pool.o ../../src/Thread_pool.cpp

block_epoll_net.o: ../../src/block_epoll_net.cpp ../../include/block_epoll_net.h \
		../../include/Thread_pool.h \
		../../include/packdef.h \
		../../include/err_str.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o block_epoll_net.o ../../src/block_epoll_net.cpp

clogic.o: ../../src/clogic.cpp ../../include/clogic.h \
		../../include/TCPKernel.h \
		../../include/block_epoll_net.h \
		../../include/Thread_pool.h \
		../../include/packdef.h \
		../../include/err_str.h \
		../../include/Mysql.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o clogic.o ../../src/clogic.cpp

err_str.o: ../../src/err_str.cpp ../../include/err_str.h \
		../../include/packdef.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o err_str.o ../../src/err_str.cpp

main.o: ../../src/main.cpp ../../include/TCPKernel.h \
		../../include/block_epoll_net.h \
		../../include/Thread_pool.h \
		../../include/packdef.h \
		../../include/err_str.h \
		../../include/Mysql.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o ../../src/main.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:


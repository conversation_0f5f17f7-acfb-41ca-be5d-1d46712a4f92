[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/Study/WeChat/WeChatServer/src/Mysql.cpp"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/src/Mysql.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/Study/WeChat/WeChatServer/src/TCPKernel.cpp"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/src/TCPKernel.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/Study/WeChat/WeChatServer/src/Thread_pool.cpp"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/src/Thread_pool.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/Study/WeChat/WeChatServer/src/block_epoll_net.cpp"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/src/block_epoll_net.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/Study/WeChat/WeChatServer/src/clogic.cpp"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/src/clogic.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/Study/WeChat/WeChatServer/src/err_str.cpp"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/src/err_str.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/Study/WeChat/WeChatServer/src/main.cpp"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/src/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/Study/WeChat/WeChatServer/include/Mysql.h"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/include/Mysql.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/Study/WeChat/WeChatServer/include/TCPKernel.h"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/include/TCPKernel.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/Study/WeChat/WeChatServer/include/Thread_pool.h"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/include/Thread_pool.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/Study/WeChat/WeChatServer/include/block_epoll_net.h"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/include/block_epoll_net.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/Study/WeChat/WeChatServer/include/clogic.h"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/include/clogic.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/Study/WeChat/WeChatServer/include/err_str.h"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/include/err_str.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_QML_DEBUG", "-DQ_CREATOR_RUN", "-I/home/<USER>/Study/WeChat/WeChatServer", "-I/home/<USER>/Study/WeChat/WeChatServer/include", "-I/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug", "-I/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/Study/WeChat/WeChatServer/include/packdef.h"], "directory": "/home/<USER>/Study/WeChat/WeChatServer/build/gcc-Debug/.qtc_clangd", "file": "/home/<USER>/Study/WeChat/WeChatServer/include/packdef.h"}]
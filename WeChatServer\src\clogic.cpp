#include "clogic.h"

void CLogic::setNetPackMap()
{
    NetPackMap(_DEF_PACK_REGISTER_RQ)    = &CLogic::RegisterRq;
    NetPackMap(_DEF_PACK_LOGIN_RQ)       = &CLogic::LoginRq;
    NetPackMap(_DEF_PACK_CREATEROOM_RQ)    =&CLogic::CreateRoomRq;
    NetPackMap(_DEF_PACK_JOINROOM_RQ)    =&CLogic::JoinRoomRq;
    NetPackMap(_DEF_PACK_LEAVEROOM_RQ)    =&CLogic::LeaveRoomRq;
}

#define _DEF_COUT_FUNC_    cout << "clientfd:"<< clientfd << __func__ << endl;


// 注册
void CLogic::RegisterRq(sock_fd clientfd, char* szbuf, int nlen) {
    printf("服务端处理注册请求");

    // 1.拆包
    STRU_REGISTER_RQ* rq = (STRU_REGISTER_RQ*)szbuf;
    STRU_REGISTER_RS rs;

    // 获取tel name password
    char sqlStr[1024] = {0};
    list<string> reslist;

    // 查表 t_user tel有没有
    sprintf(sqlStr, "SELECT tel FROM t_user WHERE tel = '%s';", rq->tel);
    if (!m_sql->SelectMysql(sqlStr, 1, reslist)) {
        printf("SelectMysql error:  %s \n", sqlStr);
        return;
    }
    if (reslist.size() > 0) {
        rs.result = tel_is_exist;
    } else {
        reslist.clear(); // 清空上一次查询结果
        sprintf(sqlStr, "SELECT name FROM t_user WHERE name = '%s';", rq->name);
        // 查询姓名
        if (!m_sql->SelectMysql(sqlStr, 1, reslist)) {
            printf("SelectMysql error:  %s \n", sqlStr);
            return;
        }
        // 用户名已经存在 注册结果为用户名已存在
        if (reslist.size() > 0) {
            rs.result = name_is_exist;
        } else { // 用户名不存在，往数据库里写数据
            rs.result = register_success;
            char sqlStr[1024] = {0};
            sprintf(sqlStr, "INSERT INTO t_user (tel, password, name, icon, feeling) VALUES ('%s', '%s', '%s', 1, '比较懒，什么也没写');",
                    rq->tel, rq->password, rq->name);
            if (!m_sql->UpdataMysql(sqlStr)) {
                printf("UpdateMysql error:  %s \n", sqlStr);
                return;
            }
        }
    }
    m_tcp->SendData(clientfd, (char*)&rs, sizeof(rs));
}
//登录
void CLogic::LoginRq(sock_fd clientfd ,char* szbuf,int nlen)
{
    printf("WeChat Server running, Server LoginRq running");
    //拆包
    STRU_LOGIN_RQ* rq = (STRU_LOGIN_RQ*)szbuf;
    STRU_LOGIN_RS rs;
    //根据tel 查询pass id
    char strSql[1024]={0};
    list<string> reslist;
    sprintf(strSql,"select password, id ,name from t_user where tel = '%s';",rq->tel);
    if(!m_sql->SelectMysql(strSql,3,reslist)){
        printf("select error",strSql);
        return ;
    }
    if(reslist.size()==0){
        //查不到 返回 没此用户
        rs.result=user_not_exist;
    }else{
        if(strcmp(rq->password,reslist.front().c_str())!=0)
        {
            //不一致 返回密码错误
            rs.result=password_error;
        }else{
            reslist.pop_front();
            //查到了 pass一致 sock保存 id -> sock映射 为了通信
            int id=atoi(reslist.front().c_str());
            reslist.pop_front();

            //保存用户sock
            UserInfo* pInfo = new UserInfo;
            pInfo->m_id = id;
            pInfo->m_roomid = 0;
            pInfo->m_sockfd = clientfd;//套
            strcpy(pInfo->m_userName,reslist.front().c_str());
            reslist.pop_front();

            //判断 id 是否在线 在线，强制下线，不在线添加
            if(m_mapIDToUserInfo.IsExist(pInfo->m_id)){
                //强制下线
            }
            m_mapIDToUserInfo.insert(pInfo->m_id,pInfo);
            //写返回包，带回id 和结果
            rs.userid=id;
            rs.result=login_success;//登录成功
            strcpy(rs.name,pInfo->m_userName);
        }


    }
    SendData(clientfd,(char*)&rs,sizeof(rs));
}
//创建房间
void CLogic::CreateRoomRq(sock_fd clientfd, char *szbuf, int nlen)
{
    printf("clientfd:%d CreateRoomRq\n",clientfd);
    //拆包
    STRU_CREATEROOM_RQ* rq = (STRU_CREATEROOM_RQ*)szbuf;
    //房间号 -18 位随机数
    int roomid=0;
    do{
        roomid = rand()%99999999 + 1;

    }while(m_mapIDToRoomid.IsExist(roomid));
    list<int> lst;
    lst.push_back(rq->m_UserID);//创建房间的人加入房间
    m_mapIDToRoomid.insert(roomid,lst);//第一个参数为房间id 第二个参数为链表（存储在房间中的人的id）

    //随机数 得到房间号  看有没有房间号 可能循环随机  map 存储 roomid ->list

    //回复
    STRU_CREATEROOM_RS rs;
    rs.m_RoomId = roomid;
    rs.m_lResult=1;
    printf("cereate roomid = %d\n",roomid);
    SendData(clientfd,(char*)&rs,sizeof(rs));


}
//加入房间
void CLogic::JoinRoomRq(sock_fd clientfd, char *szbuf, int nlen)
{
    printf("--------------------clientfd:%d JointeRoomRq------------\n",clientfd);
    //拆包
    STRU_JOINROOM_RQ* rq = (STRU_JOINROOM_RQ*)szbuf;
    //加入房间没有问题
    printf("加入房间的用户: %d\n",rq->m_UserID);
    printf("加入房间的房间: %d\n",rq->m_RoomID);
    STRU_JOINROOM_RS rs;
    //查看房间是否存在
    if(!m_mapIDToRoomid.IsExist(rq->m_RoomID)){//不存在 返回失败
        rs.m_lResult=0;//房间不存在，返回假
        SendData(clientfd,(char*)&rs,sizeof(rs));
        return ;
    }
    rs.m_lResult=1;//房间存在，返回真
    //------------------------------------
    rs.m_RoomID=rq->m_RoomID;//加入房间回复的房间id初始化
    //----------------------------------
    SendData(clientfd,(char*)&rs,sizeof(rs));

    if(!m_mapIDToUserInfo.IsExist(rq->m_UserID)) return ;//用户不存在
    UserInfo* joiner = m_mapIDToUserInfo.find(rq->m_UserID);
    STRU_ROOM_MEMBER_RQ joinrq;
    joinrq.m_UserID=rq->m_UserID;
    strcpy(joinrq.m_szUser,joiner->m_userName);

    //给自己用于更新自己的信息
    SendData(clientfd,(char*)&joinrq,sizeof(joinrq));
    //根据房间号 拿到房间成员列表
    list<int> lstRoomMen = m_mapIDToRoomid.find(rq->m_RoomID);
    //遍历列表  -- 交换信息
    for(auto ite = lstRoomMen.begin();ite!=lstRoomMen.end();++ite)
    {

        int Memid=*ite;//房间成员
        if(!m_mapIDToUserInfo.IsExist(Memid)) continue;
        UserInfo* memInfo = m_mapIDToUserInfo.find(Memid);

        //把加入人的信息发给每一个房间内成员
        STRU_ROOM_MEMBER_RQ memrq;
        memrq.m_UserID = memInfo->m_id;
        strcpy(memrq.m_szUser,memInfo->m_userName);

        SendData(memInfo->m_sockfd,(char*)&joinrq,sizeof(joinrq));

        //房间内成员每个人学校发给加入人
        SendData(clientfd,(char*)&memrq,sizeof(memrq));

    }


    //加入人，添加到房间列表
    lstRoomMen.push_back(rq->m_UserID);//把人加入到链表里面
    m_mapIDToRoomid.insert(rq->m_RoomID,lstRoomMen);

}
//离开房间
void CLogic::LeaveRoomRq(sock_fd clientfd, char *szbuf, int nlen)
{
    printf("Wechat server, LeaveRoom running");
    //拆包
    STRU_LEAVEROOM_RQ* rq = (STRU_LEAVEROOM_RQ*)szbuf;
    //看房间是否存在
    if(m_mapIDToRoomid.IsExist((rq->m_RoomId))) return;
    //如果房间存在，可以获取用户用户列表
    list<int> lst = m_mapIDToRoomid.find(rq->m_RoomId);
    //遍历每个用户 用户是否在线 在线转发
    for(auto ite = lst.begin();ite!=lst.end();)
    {
        int userid = *ite;//自带++
        if(userid == rq->m_nUserId)
        {
            ite=lst.erase(ite);
        }else{
            //用户是否在线，在线转发
            if(m_mapIDToUserInfo.IsExist(userid))
            {
                UserInfo * info = m_mapIDToUserInfo.find(userid);
                SendData(info->m_sockfd,szbuf,nlen);
            }
            ++ite;
        }
    }
    //列表是否节点为空 0 ->  map去掉
    if(lst.size() == 0)
    {
        m_mapIDToRoomid.erase(rq->m_RoomId);
        return;
    }
    //更新房间成员列表
    m_mapIDToRoomid.insert(rq->m_RoomId,lst);//更新

}

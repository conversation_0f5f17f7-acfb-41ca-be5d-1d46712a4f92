#ifndef USERSHOW_H
#define USERSHOW_H

#include <QWidget>
#include <QPaintEvent>  //添加会话事件
#include <QImage>//背景
namespace Ui {
class UserShow;
}

class UserShow : public QWidget
{
    Q_OBJECT

public:
    explicit UserShow(QWidget *parent = nullptr);
    ~UserShow();

public slots:
    //设置用户信息
    void slot_setInfo(int id,QString name);
    void paintEvent(QPaintEvent* event);

private:
    Ui::UserShow *ui;

    //设置测试
    int m_id;
    QString m_userName;
    QImage m_img;

    //友元函数
    friend class RoomDialog;
};

#endif // USERSHOW_H
